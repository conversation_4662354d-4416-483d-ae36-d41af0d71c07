<?php

/**
 * 扫描 Services/Api 目录中的所有服务文件
 * 检查每个 public 方法是否包含 'services_data' => $services_data 字符串
 * 增强版本 - 更严格的检查和验证
 */

$servicesDir = 'php/api/app/Services/Api';
$targetString = "'services_data' => \$services_data";

// 获取所有PHP文件
$files = glob($servicesDir . '/*.php');
sort($files);

$results = [];
$totalFiles = 0;
$totalMethods = 0;
$missingMethods = 0;
$detailedLog = [];

echo "开始扫描 Services/Api 目录中的所有服务文件...\n";
echo "目标字符串: " . $targetString . "\n";
echo "增强检查模式 - 100% 确认\n";
echo "=" . str_repeat("=", 80) . "\n\n";

foreach ($files as $file) {
    $totalFiles++;
    $filename = basename($file);
    echo "正在扫描: {$filename}\n";

    $content = file_get_contents($file);
    if ($content === false) {
        echo "  错误: 无法读取文件 {$filename}\n";
        continue;
    }

    // 使用更精确的正则表达式匹配所有 public 方法
    $pattern = '/public\s+(?:static\s+)?function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{/s';
    preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE);

    $methodsInFile = [];
    $missingInFile = [];
    $fileLog = [
        'filename' => $filename,
        'methods' => []
    ];

    foreach ($matches[1] as $index => $match) {
        $methodName = $match[0];
        $methodStart = $matches[0][$index][1];

        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }

        $totalMethods++;
        $methodsInFile[] = $methodName;

        // 找到方法的结束位置
        $methodEnd = findMethodEnd($content, $methodStart);
        $methodContent = substr($content, $methodStart, $methodEnd - $methodStart);

        // 多种方式检查是否包含目标字符串
        $hasTargetString = false;
        $checkResults = [];

        // 检查1: 精确匹配
        if (strpos($methodContent, $targetString) !== false) {
            $hasTargetString = true;
            $checkResults[] = "精确匹配";
        }

        // 检查2: 变体匹配（考虑空格差异）
        $targetVariants = [
            "'services_data' => \$services_data",
            "'services_data'=>\$services_data",
            "\"services_data\" => \$services_data",
            "\"services_data\"=>\$services_data"
        ];

        foreach ($targetVariants as $variant) {
            if (strpos($methodContent, $variant) !== false) {
                $hasTargetString = true;
                $checkResults[] = "变体匹配: $variant";
                break;
            }
        }

        // 记录详细信息
        $methodLog = [
            'name' => $methodName,
            'has_target' => $hasTargetString,
            'check_results' => $checkResults,
            'method_length' => strlen($methodContent)
        ];

        if (!$hasTargetString) {
            $missingInFile[] = $methodName;
            $missingMethods++;
            // 记录方法内容的前200个字符用于调试
            $methodLog['preview'] = substr(str_replace(["\n", "\r"], " ", $methodContent), 0, 200) . "...";
        }

        $fileLog['methods'][] = $methodLog;
    }

    $detailedLog[] = $fileLog;

    if (!empty($missingInFile)) {
        $results[] = [
            'file' => $filename,
            'methods' => $missingInFile,
            'total_methods' => count($methodsInFile)
        ];
    }

    echo "  找到 " . count($methodsInFile) . " 个 public 方法";
    if (!empty($missingInFile)) {
        echo "，其中 " . count($missingInFile) . " 个缺少目标字符串";
        echo " [" . implode(", ", $missingInFile) . "]";
    }
    echo "\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "扫描完成统计:\n";
echo "- 总文件数: {$totalFiles}\n";
echo "- 总方法数: {$totalMethods}\n";
echo "- 缺少目标字符串的方法数: {$missingMethods}\n";
echo "\n";

// 生成详细报告
if (!empty($results)) {
    echo "## 缺少 'services_data' => \$services_data 的方法报告\n\n";

    $counter = 1;
    foreach ($results as $result) {
        echo "### {$counter}. {$result['file']} (" . count($result['methods']) . "个方法)\n";
        foreach ($result['methods'] as $method) {
            echo "- [ ] `{$method}()`\n";
        }
        echo "\n";
        $counter++;
    }

    // 输出详细调试信息
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "详细调试信息:\n";
    echo str_repeat("=", 80) . "\n";

    foreach ($detailedLog as $fileLog) {
        $hasMissing = false;
        foreach ($fileLog['methods'] as $method) {
            if (!$method['has_target']) {
                $hasMissing = true;
                break;
            }
        }

        if ($hasMissing) {
            echo "\n文件: {$fileLog['filename']}\n";
            foreach ($fileLog['methods'] as $method) {
                if (!$method['has_target']) {
                    echo "  方法: {$method['name']}()\n";
                    echo "  长度: {$method['method_length']} 字符\n";
                    echo "  预览: {$method['preview']}\n";
                    echo "  ---\n";
                }
            }
        }
    }
} else {
    echo "✅ 所有 public 方法都包含目标字符串！\n";

    // 验证结果 - 随机抽查几个文件
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "验证抽查 (随机检查5个文件的详细情况):\n";
    echo str_repeat("=", 80) . "\n";

    $randomFiles = array_rand($detailedLog, min(5, count($detailedLog)));
    if (!is_array($randomFiles)) {
        $randomFiles = [$randomFiles];
    }

    foreach ($randomFiles as $index) {
        $fileLog = $detailedLog[$index];
        echo "\n文件: {$fileLog['filename']}\n";
        foreach ($fileLog['methods'] as $method) {
            $status = $method['has_target'] ? "✅" : "❌";
            echo "  {$status} {$method['name']}() - " . implode(", ", $method['check_results']) . "\n";
        }
    }
}

/**
 * 找到方法的结束位置
 */
function findMethodEnd($content, $start) {
    $braceCount = 0;
    $inMethod = false;
    $length = strlen($content);
    
    for ($i = $start; $i < $length; $i++) {
        $char = $content[$i];
        
        if ($char === '{') {
            $braceCount++;
            $inMethod = true;
        } elseif ($char === '}') {
            $braceCount--;
            if ($inMethod && $braceCount === 0) {
                return $i + 1;
            }
        }
    }
    
    return $length;
}

?>
