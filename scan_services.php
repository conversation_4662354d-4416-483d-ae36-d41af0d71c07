<?php

/**
 * 扫描 Services/Api 目录中的所有服务文件
 * 检查每个 public 方法是否包含 'services_data' => $services_data 字符串
 */

$servicesDir = 'php/api/app/Services/Api';
$targetString = "'services_data' => \$services_data";

// 获取所有PHP文件
$files = glob($servicesDir . '/*.php');
sort($files);

$results = [];
$totalFiles = 0;
$totalMethods = 0;
$missingMethods = 0;

echo "开始扫描 Services/Api 目录中的所有服务文件...\n";
echo "目标字符串: " . $targetString . "\n";
echo "=" . str_repeat("=", 80) . "\n\n";

foreach ($files as $file) {
    $totalFiles++;
    $filename = basename($file);
    echo "正在扫描: {$filename}\n";
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  错误: 无法读取文件 {$filename}\n";
        continue;
    }
    
    // 使用正则表达式匹配所有 public 方法
    $pattern = '/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{/';
    preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
    
    $methodsInFile = [];
    $missingInFile = [];
    
    foreach ($matches[1] as $index => $match) {
        $methodName = $match[0];
        $methodStart = $matches[0][$index][1];
        
        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }
        
        $totalMethods++;
        $methodsInFile[] = $methodName;
        
        // 找到方法的结束位置
        $methodEnd = findMethodEnd($content, $methodStart);
        $methodContent = substr($content, $methodStart, $methodEnd - $methodStart);
        
        // 检查是否包含目标字符串
        if (strpos($methodContent, $targetString) === false) {
            $missingInFile[] = $methodName;
            $missingMethods++;
        }
    }
    
    if (!empty($missingInFile)) {
        $results[] = [
            'file' => $filename,
            'methods' => $missingInFile,
            'total_methods' => count($methodsInFile)
        ];
    }
    
    echo "  找到 " . count($methodsInFile) . " 个 public 方法";
    if (!empty($missingInFile)) {
        echo "，其中 " . count($missingInFile) . " 个缺少目标字符串";
    }
    echo "\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "扫描完成统计:\n";
echo "- 总文件数: {$totalFiles}\n";
echo "- 总方法数: {$totalMethods}\n";
echo "- 缺少目标字符串的方法数: {$missingMethods}\n";
echo "\n";

// 生成报告
if (!empty($results)) {
    echo "## 缺少 'services_data' => \$services_data 的方法报告\n\n";
    
    $counter = 1;
    foreach ($results as $result) {
        echo "### {$counter}. {$result['file']} (" . count($result['methods']) . "个方法)\n";
        foreach ($result['methods'] as $method) {
            echo "- [ ] `{$method}()`\n";
        }
        echo "\n";
        $counter++;
    }
} else {
    echo "✅ 所有 public 方法都包含目标字符串！\n";
}

/**
 * 找到方法的结束位置
 */
function findMethodEnd($content, $start) {
    $braceCount = 0;
    $inMethod = false;
    $length = strlen($content);
    
    for ($i = $start; $i < $length; $i++) {
        $char = $content[$i];
        
        if ($char === '{') {
            $braceCount++;
            $inMethod = true;
        } elseif ($char === '}') {
            $braceCount--;
            if ($inMethod && $braceCount === 0) {
                return $i + 1;
            }
        }
    }
    
    return $length;
}

?>
